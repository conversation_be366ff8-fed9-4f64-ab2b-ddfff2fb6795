<template>
    <div class="w-100 h-100 position-relative alarm-list-container">
        <div class="scroll-container" ref="scrollContainer" @mouseover="stopScrolling" @mouseleave="startScrolling">
            <div class="scroll-content" :style="scrollStyle">
                <ul class="area-list">
                    <li v-for="(i, index) in baselist" :key="i.ID"
                        :class="['area-item flex-item cursor-pointer', { 'odd-item': index % 2 === 0 }]"
                       >
                        <div class="d-flex align-items-center justify-content-center area-item-img">
                            <img :src="getImagePath(i.type)" alt="">
                        </div>
                        <div class="area-item-content">
                            <div class="flex-content information-title d-flex align-items-center">{{ i.message }}
                            </div>
                            <div class=" d-flex flex-row justify-content-between">
                                <div class="area-item-left">
                                    <span>当前值为</span>
                                    <span :class="i.type==1? 'error1':'error2'" style="padding-left: 5px;">{{ i.value }}</span>
                                </div>
                                <div class="area-item-right">
                                    <span>{{ i.time }}</span>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li v-if="needExtraItem" :key="'extra-item'" class="area-item flex-item"></li>
                </ul>
                <ul class="area-list" v-if="shouldScroll">
                    <li v-for="(i, index) in baselist" :key="i.ID"
                        :class="['area-item flex-item cursor-pointer', { 'odd-item': index % 2 === 0 }]"
                       >
                        <div class="d-flex align-items-center justify-content-center area-item-img">
                            <img :src="getImagePath(i.type)" alt="">
                        </div>
                        <div class="area-item-content">
                            <div class="flex-content information-title d-flex align-items-center">{{ i.message }}
                            </div>
                            <div class=" d-flex flex-row justify-content-between">
                                <div class="area-item-left">
                                    <span>当前值为</span>
                                    <span :class="i.type==1? 'error1':'error2'" style="padding-left: 5px;">{{ i.value }}</span>
                                </div>
                                <div class="area-item-right">
                                    <span>{{ i.time }}</span>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li v-if="needExtraItem" :key="'extra-item'" class="area-item flex-item"></li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
import error1 from '@/assets/ui/error1.png'
import error2 from '@/assets/ui/error2.png'

export default {
    props: {
        baselist: {
            type: Array,
            required: true
        },
    },
    data() {
        return {
            scrollOffset: 0,
            scrollInterval: null,
            contentHeight: 0,
        }
    },
    computed: {
        needExtraItem() {
            return this.baselist.length % 2 !== 0 && this.baselist.length > 3;
        },
        shouldScroll() {
            return this.baselist.length > 1;
        },
        scrollStyle() {
            return this.shouldScroll
                ? { transform: `translateY(${-this.scrollOffset}px)` }
                : {};
        }
    },
    watch: {
        baselist: {
            handler() {
                this.resetScroll();
            },
            deep: true
        }
    },
    mounted() {
        this.resetScroll();
    },
    methods: {
        getImagePath(type) {
         return type == 1 
           ? error1
           : error2;
       },
        resetScroll() {
            this.stopScrolling();
            this.$nextTick(() => {
                const container = this.$refs.scrollContainer;
                this.contentHeight = this.shouldScroll ? container.children[0].offsetHeight / 2 : 0;
                this.scrollOffset = 0;
                if (this.shouldScroll) {
                    this.startScrolling();
                }
            });
        },
        startScrolling() {
            if (!this.shouldScroll) return;

            const scrollStep = 0.5; // 可以根据需要调整滚动速度

            let lastTime = performance.now();
            const step = (currentTime) => {
                const deltaTime = currentTime - lastTime;
                lastTime = currentTime;

                this.scrollOffset += scrollStep * (deltaTime / 16.67); // 根据实际帧率调整滚动速度
                if (this.scrollOffset >= this.contentHeight) {
                    this.scrollOffset = 0;
                }
                this.scrollInterval = requestAnimationFrame(step);
            };

            this.scrollInterval = requestAnimationFrame(step);
        },
        stopScrolling() {
            if (this.scrollInterval) {
                cancelAnimationFrame(this.scrollInterval);
                this.scrollInterval = null;
            }
        }
    },
    beforeDestroy() {
        this.stopScrolling();
    }
}
</script>


<style lang="less" scoped>
.error1{
    color: #f15a24;
}
.error2{
    color: #eeac46;
}
.alarm-list-container {
  display: flex;
  flex-direction: column;
}

.scroll-container {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.scroll-content {
    width: 100%;
    will-change: transform;
}

.area-list {
    width: 100%;
    padding: 0;
    margin: 0;
    list-style-type: none;
}

.area-item {
  height: 45px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #BDDBF1;
  padding: 12px;
  display: flex;
  align-items: center;

  .area-item-img {
    height: 39px;
    width: 39px;
    flex-shrink: 0;

    img {
      height: 100%;
      width: 100%;
    }
  }

  .area-item-content {
    padding-left: 1rem;
    width: calc(100% - 39px - 1rem);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    flex: 1;
  }
}
 

.area-item-left  
.area-item-right
{
    height: 100%;
    // width: 50%;
}

.odd-item {
    background-color: rgba(1, 10, 20, .6);
}
</style>