<template>
    <div class="w-100 h-100">
    <section-title title="基地列表" class="white-title" />
    <div class="list-container">
          <ul class="area-list">
            <li v-for="(i, index) in localBaselist" :key="i.uniqueKey" 
          :class="['area-item flex-item cursor-pointer', { 'odd-item': index % 2 === 0 }]" @click="linkshebei(i)">
              <div class="d-flex h-100 align-items-center justify-content-center">
                <img src="~@/assets/mobile/base.png" alt="">
              </div>
              <div class="flex-content information-title d-flex align-items-center">{{ i.BASE_NAME }}</div>
              <div class="d-flex align-items-center">运行良好</div>
            </li>
          </ul>
      </div>
      
      <section-title title="设备描述" class="white-title mt-3" />
      <div class="device-container">
        <div class="device-row">
            <div class="device-info">
                <div class="device-icon">
                    <img src="~@/assets/mobile/device-icon-ts.png" alt="">
                </div>
                <div class="device-text">
                    <div class="device-name">投饲机</div>
                    <div class="device-number">
                        <span>6</span>
                        <span>台</span>
                    </div>
                    <div class="device-line"></div>
                </div>
            </div>
            <div class="device-info">
                <div class="device-icon">
                    <img src="~@/assets/mobile/device-icon-o2.png" alt="">
                </div>
                <div class="device-text">
                    <div class="device-name">增氧机</div>
                    <div class="device-number">
                        <span>6</span>
                        <span>台</span>
                    </div>
                    <div class="device-line"></div>
                </div>
            </div>
        </div>
        <div class="device-row">
            <div class="device-info">
                <div class="device-icon">
                    <img src="~@/assets/mobile/device-icon-ph.png" alt="">
                </div>
                <div class="device-text">
                    <div class="device-name">水质监测</div>
                    <div class="device-number">
                        <span>8</span>
                        <span>套</span>
                    </div>
                    <div class="device-line"></div>
                </div>
            </div>
            <div class="device-info">
                <div class="device-icon">
                    <img src="~@/assets/mobile/device-icon-ts.png" alt="">
                </div>
                <div class="device-text">
                    <div class="device-name">气象监测站</div>
                    <div class="device-number">
                        <span>8</span>
                        <span>套</span>
                    </div>
                    <div class="device-line"></div>
                </div>
            </div>
        </div>
    </div>
    </div>
  </template>
  
  <script>
import SectionTitle from './SectionTitle.vue'
import { shebeilistv2, select_equipmentv2, caicon } from '@/request/api'
import mobileMapBg1 from '@/assets/ui/map-bg1.png'
import mobileMapBg2 from '@/assets/ui/map-bg2.png'

  export default {
  name: 'BaseList',
  components: {
    SectionTitle
  },
    props: {
      baselist: {
        type: Array,
        required: true
    }
    },
    data() {
      return {
      localBaselist: [], // 本地数据属性

      shebeilist: {
        "water": [],
        "meteorological": [],
        "monitoring": []
      },
      formattedData: [
        { label: '设备名称', value: '' },
        { label: '设备描述', value: '' },
        { label: '运行状态', value: '' },
        { label: '电压', value: '' },
        { label: '光照', value: '' },
        { label: '风速', value: '' },
        { label: '风向', value: '' },
        { label: '辐射', value: '' },
        { label: 'ORP', value: '' },
        { label: 'CON', value: '' },
      ],
      miaoshu: { EQUIPMENT_NAME: '', INTRODUCE: '', Online_Voltage: '', state: '', LIGHT: '', WIND_SPEED: '', WIND_DIRECTION: '', RADIATION: '', ORP: '', CON: '', type: 1 },

      lineData: [
        {
          name: "温度",
          data: [],
          xAxisData: [],
          data1: [],
          data2: [],
        }, {
          name: "溶氧值",
          data: [],
          xAxisData: [],
          data1: [],
          data2: [],
        }, {
          name: "PH",
          data: [],
          xAxisData: [],
          data1: [],
          data2: [],
        }, {
          name: "雨量",
          data: [],
          xAxisData: [],
          data1: [],
          data2: [],
        }
      ],

      computed: {
        backgroundImage() {
          if (this.miaoshu.EQUIPMENT_TYPE === "水质检测") {
            return mobileMapBg1;
          } else {
            return mobileMapBg2;
          }
        },
        labelcolor() {
          return this.miaoshu.EQUIPMENT_TYPE == "水质检测" ? '#5CA0B1' : '#99BB6D'
        },
        valuecolor() {
          return this.miaoshu.EQUIPMENT_TYPE == "水质检测" ? '#c6e1e7' : '#e9f6d8'
        },
        miaoshuFormatted() {
          if (this.miaoshu.EQUIPMENT_TYPE == "水质检测") {
            const formattedData = [
              { label: '设备名称', value: this.miaoshu.EQUIPMENT_NAME },
              { label: '采集时间', value: this.miaoshu.ACQUISITION_TIME },
              { label: '设备描述', value: this.miaoshu.INTRODUCE },
              { label: '电压', value: this.miaoshu.VOLTAGE },
              { label: '状态', value: this.miaoshu.state },
              { label: 'ORP', value: this.miaoshu.ORP },
              { label: '电导', value: this.miaoshu.CON },
              { label: '温度', value: this.miaoshu.TEM },
              { label: 'PH', value: this.miaoshu.PH },
              { label: '含氧量', value: this.miaoshu.O2 },
              { label: '盐度', value: this.miaoshu.SALT }
            ];
            return formattedData;
          }
          else if (this.miaoshu.EQUIPMENT_TYPE == "气象检测") {
            const formattedData = [
              { label: '设备名称', value: this.miaoshu.EQUIPMENT_NAME },
              { label: '采集时间', value: this.miaoshu.ACQUISITION_TIME },
              { label: '设备描述', value: this.miaoshu.INTRODUCE },
              { label: '电压', value: this.miaoshu.VOLTAGE },
              { label: '状态', value: this.miaoshu.state },
              { label: '光照', value: this.miaoshu.LIGHT },
              { label: '风速', value: this.miaoshu.WIND_SPEED },
              { label: '风向', value: this.miaoshu.WIND_DIRECTION },
              { label: '辐射', value: this.miaoshu.RADIATION },
              { label: '温度', value: this.miaoshu.TEM },
              { label: '湿度', value: this.miaoshu.HUMIDITY }
              // { label: '气压', value: this.miaoshu.ATM },
              // { label: '雨量', value: this.miaoshu.RAINFALL }
            ];
            return formattedData;
          }
          return [];
        },
      },

      mounted() {
        this.getlist();
        // this.startPolling(); // 开始定时请求数据
      },

      methods: {
        startPolling() {
          this.intervalId = setInterval(() => {
            // this.getlist();
          }, 60000); // 每60秒（1分钟）调用一次getlist
        },
        addAlarm(message, value, type, time) {
          this.alertYuanList.push({
            message,
            value,
            type,
            time,
          });
        },
        getCurrentTime() {
          const now = new Date();
          return `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;
        },

        handleLinkShebei(type, item, index) {
          if (type == "water") {
            this.selectid = 1
          } else if (type == "meteorological") {
            this.selectid = 2
          } else if (type == "monitoring") {
            this.selectid = 3
          }
          if (this.selectid != 3) {
            // 查找对应的坐标
            // console.log("item", item);
            if (item.UPDATE_TIME) {
              this.currentTime = item.UPDATE_TIME.replace('T', ' ');
            }
            this.selectEquipment(item, 1)
            const selectedCoordinate = this.zuobiaolist.find(coord =>
              coord.toString() === item.EQUIPMENT_LOCATION
            );

            if (selectedCoordinate) {
              // 如果找到对应的坐标，将地图中心设置为该坐标
              this.map.setCenter(selectedCoordinate);

              // 可选：调整缩放级别
              this.map.setZoom(18);

              // 可选：添加动画效果
              this.map.panTo(selectedCoordinate);
            }

            // 更新 miaoshu 数据
            this.miaoshu = item;
            this.miaoshu.state = item.STATE == 0 ? "停止" : "正常";
            this.miaoshu.type = type == "water" ? 1 : 2;
            // // 调用 addEquipment 方法（如果需要的话）
            //   this.addEquipment(type == "water" ? 0 : 1);
            // 在这里处理点击事件
            this.getshebeidata(item);
          }
          else {
            if (index % 2 == 0) {
              this.initPlayer(item.video_address, item.serial_number);
            }
            else {
              this.initPlayer1(item.video_address, item.serial_number);
            }
          }
          if (this.selectid == 3) {
            if (index % 2 == 0) {
              this.initPlayer(item.video_address, item.serial_number);
            }
            else {
              this.initPlayer1(item.video_address, item.serial_number);
            }

            if (item.camera_location) {
              const position = item.camera_location.split(',');
              if (position) {
                // 设置地图中心点
                this.map.setCenter(position);
                this.map.setZoom(18);
                this.map.panTo(position);
              }
              // 不直接播放视频，等待用户点击地图上的标记
            }

            return;
          }

          // 您可以在这里添加您原来在 linkshebei 方法中的逻辑
        },
        getshebeidata(item) {
          select_equipmentv2({
            base_id: this.$route.query.base_id,
            equipmentID: item.ID
          }).then(res => {
            const data = res.data.shuju
            this.lineData = []

            this.lineData = [
              {
                name: "温度",
                id: 1,
                data: data.templist[0].y[0].data,
                xAxisData: data.templist[0].x,
                data1: data.templist[0].y[1].data,
                data2: data.templist[0].y[2].data,
                max: data.templist[0].max,
                min: data.templist[0].min,
              }
            ]
            if (this.selectid == 1) {
              this.lineData.push(
                {
                  name: "溶氧值",
                  id: 2,
                  data: data.templist[1].y[0].data,
                  xAxisData: data.templist[0].x,
                  data1: data.templist[1].y[1].data,
                  data2: data.templist[1].y[2].data,
                  max: data.templist[1].max,
                  min: data.templist[1].min,
                }, {
                name: "PH",
                id: 3,
                data: data.templist[2].y[0].data,
                xAxisData: data.templist[0].x,
                data1: data.templist[2].y[1].data,
                data2: data.templist[2].y[2].data,
                max: data.templist[2].max,
                min: data.templist[2].min,
              }
              )
            }
            else if (this.selectid == 2) {
              this.lineData.push(
                {
                  name: "溶氧值",
                  id: 2,
                  data: data.templist[3].y[0].data,
                  xAxisData: data.templist[0].x,
                  data1: data.templist[3].y[1].data,
                  data2: data.templist[3].y[2].data,
                  max: data.templist[3].max,
                  min: data.templist[3].min,
                }, {
                name: "PH",
                id: 3,
                data: data.templist[4].y[0].data,
                xAxisData: data.templist[0].x,
                data1: data.templist[4].y[1].data,
                data2: data.templist[4].y[2].data,
                max: data.templist[4].max,
                min: data.templist[4].min,
              }, {
                name: "雨量",
                id: 4,
                data: data.templist[5].y[0].data,
                xAxisData: data.templist[0].x,
                data1: data.templist[5].y[1].data,
                data2: data.templist[5].y[2].data,
                max: data.templist[5].max,
                min: data.templist[5].min,
              })
            }
          })
        },
        getlist() {
          this.base_id = this.$route.query.base_id
          shebeilistv2({
            base_id: this.base_id,
          }).then(res => {
            console.log('数据接口:', res);
            this.getshebeidata(res.data.shebeilist[0]);
            // console.log("res",res);
            let tianqi = res.data.tianqi;
            Object.keys(tianqi).forEach(key => {
              if (Object.prototype.hasOwnProperty.call(this.wetherinfo, key)) {
                this.wetherinfo[key].value = parseFloat(parseFloat(tianqi[key]).toFixed(1));
              }
            });
            this.shelist = res.data.shebeilist;
            this.shelist.map(item => {
              item.UANDL = JSON.parse(item.UANDL)
              if (item.EQUIPMENT_TYPE == '水质检测') {
                this.shebeilist.water.push(item)
              } else if (item.EQUIPMENT_TYPE == '气象检测') {
                this.shebeilist.meteorological.push(item)
              } else if (item.EQUIPMENT_TYPE == '监控检测') {
                this.shebeilist.monitoring.push(item)
              }
            })
            for (let i = 1; i < this.shelist.length; i++) {
              const device = this.shelist[i];
              const Online_Voltage = device.Online_Voltage;
              // 检查设备类型是否为水质设备
              if (device.EQUIPMENT_TYPE === '水质检测') {
                const oxygenLevel = device.O2;
                const temperature = device.TEM;
                const pHValue = device.PH;
                const UANDL = device.UANDL;
                const time = device.UPDATE_TIME.replace('T', ' ');

                let parsedUANDL = UANDL;
                if (typeof UANDL === 'string') {
                  try {
                    parsedUANDL = JSON.parse(UANDL);
                  } catch (error) {
                    console.error('解析 UANDL 失败:', error);
                  }
                } else if (typeof UANDL !== 'object' || UANDL === null) {
                  console.error('UANDL 不是有效的对象或 JSON 字符串');
                  return; // 或者进行其他错误处理
                }
                if (oxygenLevel < parsedUANDL.o2.th[0] || oxygenLevel > parsedUANDL.o2.th[1]) {
                  const alarmMessage = `${device.EQUIPMENT_NAME}的溶氧值异常`;
                  const value = oxygenLevel
                  this.addAlarm(alarmMessage, value, 1, time);
                }
                if (temperature < parsedUANDL.te.th[0] || temperature > parsedUANDL.te.th[1]) {
                  const alarmMessage = `${device.EQUIPMENT_NAME}的温度异常`;
                  const value = temperature
                  this.addAlarm(alarmMessage, value, 1, time);
                }

                if (pHValue < parsedUANDL.ph.th[0] || pHValue > parsedUANDL.ph.th[1]) {
                  const alarmMessage = `${device.EQUIPMENT_NAME}的PH值异常`;
                  const value = pHValue
                  this.addAlarm(alarmMessage, value, 1, time);
                }
                if (Online_Voltage == 0 || Online_Voltage < 10) {
                  const alarmMessage = `${device.EQUIPMENT_NAME}的电压值异常`;
                  const value = Online_Voltage
                  this.addAlarm(alarmMessage, value, 2, time);
                }
              }
            }
            this.zuobiaolist = res.data.zuobiaoilist;
            this.inforlist = res.data.inforlist;
            this.alertlist = res.data.alertlist;
            this.tianqi = res.data.tianqi;
            this.chuangan2 = res.data.chuangan2; //所有设备的传感器参数（最新的一条）
            this.chuangan = res.data.chuangan;  //传感器传来的设备一参数,用来初始化

            // this.PH = res.data.shebeilist[0].PH
            // this.TEM = res.data.shebeilist[0].TEM //初始温度
            // this.O2 = res.data.shebeilist[0].O2
            // this.SALT = res.data.shebeilist[0].SALT
            // this.CON = res.data.shebeilist[0].CON

            // this.shebeiname="设备参数  " +  this.chuangan.EQUIPMENT_NAME

            // this.shuju = res.data.shuju;
            this.shuju2 = res.data.shuju2;
            this.aa = this.shuju2.templist[0];//默认折线图、设备一参数 
            this.renderMap();
            this.handleLinkShebei('water', { ID: 1 })
            this.selectEquipment(this.shelist[0], 1) //默认选择第一个设备展示
          })
        },

        async getEquipmentType(local, base_id) {
          let type = '水质检测' // 默认类型
          await caicon({ zuobiao: local, base_id: base_id }).then(res => {
            if (res && res.data) {
              type = res.data
            }
          })
          return type
        },
        onClickLeft() {
          this.$router.back();
        },
        emitLinkShebei(type, item, index) {
          this.$emit('link-shebei', type, item, index);
        },
        linkshebei(item) {
          // 将基地ID存储到sessionStorage中
          sessionStorage.setItem('base_id', item.ID);
          console.log('已将基地ID存储到sessionStorage:', item.ID);
          
          this.$router.push({
            path: '/detail',
            query: {
              base_id: item.ID,
              base_name: item.BASE_NAME
            }
          });
        }
      }


      }
    },
    watch: {
      baselist: {
        immediate: true, 
      handler(newVal) {
        this.localBaselist = newVal.map((item, index) => ({
          ...item,
          uniqueKey: `${item.ID}-${index}`
        }));
      }
    }
      },
  methods: {
      linkshebei(item) {
        // 将基地ID存储到sessionStorage中
        sessionStorage.setItem('base_id', item.ID);
        console.log('已将基地ID存储到sessionStorage:', item.ID);
        
        this.$router.push({
          path: '/detail',
          query: {
            base_id: item.ID,
            base_name: item.BASE_NAME
          }
        });
    }
    }
  }
  </script>
  
  <style lang="less" scoped>
.list-container {
    width: 100%;
    height: calc(100% - 50px);
    max-height: 250px;
    overflow-y: auto;
    display: flex;
    justify-content: center;
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-thumb {
      background-color: rgba(67, 200, 255, 0.5);
      border-radius: 3px;
    }
    &::-webkit-scrollbar-track {
      background-color: rgba(1, 10, 20, 0.3);
    }
  }
  
  .area-list {
    width: 100%;
    padding: 0;
    margin: 0;
    list-style-type: none;
  }
  
  .area-item {
    height: 50px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #BDDBF1;
    line-height: 28px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  
    div:nth-child(1) {
      padding-left: 1rem;
      height: 20px;
      width: 20px;
      display: flex;
      align-items: center;
      justify-content: center;

      img {
        height: 20px;
        width: 20px;
        object-fit: contain;
      }
    }
  
    div:nth-child(2) {
      padding-left: 1rem;
      flex: 1;
    }
  
    div:nth-child(3) {
      padding-right: 1rem;
      color: rgba(67, 200, 255, 1);
    }
  }
  
  .odd-item {
    background-color: rgba(1, 10, 20, .6);
  }

.device-container {
  width: 100%;
  height: 200px;
  display: flex;
  flex-direction: column;
}

.device-row {
  width: 100%;
  height: 50%;
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.device-info {
  height: 90%;
  width: 48%;
  background-image: url("~@/assets/mobile/device-bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: row;
}

.device-icon {
    height: 100%;
    width: 28%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding-left: 1rem;

    img {
        height: 53px;
        width: 60px;
    }
}

.device-text {
    height: 70%;
    width: 72%;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
    font-family: PingFang SC;
    font-weight: 600;
    font-size: 16px;
    color: #BDDBF1;
    line-height: 28px;
    padding-right: 0.6rem;
    padding-top: 0.5rem;
    .device-name{
        width: 100%;
        height: 50%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        
    }
    .device-number{
        width: 80%;
        height: 50%;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        span:nth-child(1){
            font-size: 26px;
            text-shadow: 0px 2px 5px rgba(158,184,218,0.9);
            margin-right: 5px;
        }
    }
    .device-line{
        background: linear-gradient(-90deg, rgba(158,184,218,0.2) 0%, #9EB8DA 100%);
        opacity: 0.5;
        height: 2px;
        width: 100%;
    }
}
</style>