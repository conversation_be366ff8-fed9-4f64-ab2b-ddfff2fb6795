<template>
  <div class="page-wrapper w-100 h-100 ">
    <div class="w-100 h-100 map-container" id="map-container" ></div>

    <Head :tianqi="tianqi">
      <div class="header-box text-max d-flex justify-content-center align-items-center pb-4">
        <div class="d-flex align-items-center justify-content-center mr-5">
          <div class="button-item  mr-3 information-title" @click="linkyangzhi()">养殖日志</div>
          <div class="button-item mr-3 information-title">智能控制</div>
          <div class="button-item mr-3 information-title" @click="$router.push('/home')">返回上级</div>
          <div class="mx-5">{{ base_name || '上海海洋大学智慧养殖平台' }}</div>
          <div class="button-item information-title" @click="linkhistory()">数据中心</div>

        </div>

      </div>

    </Head>
    <aside class="common-box position-absolute top-0 left-box ">

      <div class="h-70 pb-2 border-box">
        <Item title="设备列表">
          <ul class="area-list">
            <li>
              <span @click="showToggle(1)">水质设备</span>
              <ul v-show="isShow">
                <li v-for="(i, index) in shelist" :key="index" class="flex-item mb-3" v-show="i.EQUIPMENT_TYPE == '水质检测'">

                  <div class="flex-content information-title" @click="selectEquipment(i, index); addEquipment(0)">
                    {{ i.BACILITIES_NAME }}->{{ i.EQUIPMENT_NAME }}</div>
                  <div class="tag" :class="{ 'tag-success': i.STATE === 2 }">运行正常</div>
                </li>
              </ul>
            </li>
            <li>
              <span @click="showToggle(2)">气象设备</span>
              <ul v-show="isShow1">
                <li v-for="(i, index) in shelist" :key="index" class="flex-item mb-3" v-show="i.EQUIPMENT_TYPE == '气象检测'">
                  <div class="flex-content information-title" @click="selectEquipment(i, index); addEquipment(1)">
                    {{ i.BACILITIES_NAME }}->{{ i.EQUIPMENT_NAME }}</div>
                  <div class="tag" :class="{ 'tag-success': i.STATE === 2 }">运行正常</div>
                </li>
              </ul>
            </li>

          </ul>
        </Item>

      </div>
      <div class=" area-list2 h-30">
        <Item title="报警信息">
          <ul class="error-list" style="max-height: 200px; overflow-y: auto; font-size: 18px;">
            <li v-for="(alarm, index) in alertYuanList" :key="index"
              class="d-flex align-items-center justify-content-between mb-2">
              <div class="d-flex align-items-center">
                <img src="@/assets/img/error.png" style="width:100px;height:20px" alt="">
                <div class="ml-3">{{ alarm.message }}</div>
              </div>
              <div class="text-title">{{ alarm.time }}</div>
            </li>
          </ul>
        </Item>
      </div>
    </aside>
    <section class="bottom-box d-flex align-items-center">
      <aside class="common-box w-50 h-100 pt-4 border-box mr-2">
        <Item title="设备监控">
          <!-- <svg t="1642341515961" @click="onViewVideo(videoSrc)" class="large-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2123" width="1.6rem" height="1.6rem"><path d="M919.920093 725.414549q3.014188 26.122962 7.033105 58.776664t7.53547 66.814498 7.53547 67.819227 7.033105 60.786122q6.028376 47.222277-41.193901 44.208089-25.118232-2.009459-56.767205-5.526011t-64.805039-7.53547-65.809769-8.037834-59.781393-7.033105q-29.137149-3.014188-37.174984-16.578033t9.042564-30.644243q11.052022-10.047293 27.127691-27.630056t27.127691-28.634785q11.052022-12.056752 7.033105-22.104044t-16.075669-23.108774q-28.13242-27.127691-51.241194-49.231735t-51.241194-51.241194q-6.028376-6.028376-12.056752-13.061481t-9.042564-15.573304-1.004729-18.085127 13.061481-20.59695q6.028376-6.028376 10.047293-10.549658t8.037834-8.037834 8.540199-8.037834 11.554387-12.559116q20.094586-20.094586 37.174984-17.080398t37.174984 23.108774 41.193901 40.691536 47.222277 46.719912q19.089857 18.085127 32.653702 25.118232t26.625326-6.028376q9.042564-9.042564 22.606409-21.60168t23.611138-22.606409q17.080398-17.080398 30.644243-13.061481t16.578033 30.141879zM43.79615 383.80659q-3.014188-26.122962-7.033105-58.776664t-7.53547-66.814498-7.53547-67.819227-7.033105-60.786122q-3.014188-26.122962 6.53074-36.170255t33.658431-8.037834q25.118232 2.009459 56.767205 5.526011t64.805039 7.53547 65.809769 8.037834 59.781393 7.033105q30.141879 3.014188 37.677348 16.578033t-9.544928 30.644243q-10.047293 10.047293-24.615868 26.122962t-25.620597 27.127691q-12.056752 12.056752-8.037834 22.104044t17.080398 23.108774q13.061481 14.06621 24.615868 24.615868t22.606409 21.099315 23.108774 22.606409l25.118232 25.118232q6.028376 6.028376 11.554387 14.06621t8.037834 17.080398-0.502365 19.089857-13.061481 20.094586l-11.052022 11.052022q-4.018917 4.018917-7.53547 8.037834t-8.540199 8.037834l-11.052022 12.056752q-20.094586 20.094586-34.663161 15.070939t-34.663161-25.118232-38.179713-37.677348-44.208089-43.705724q-18.085127-18.085127-32.151337-25.118232t-27.127691 6.028376q-9.042564 10.047293-25.118232 24.615868t-26.122962 24.615868q-17.080398 17.080398-30.141879 13.061481t-16.075669-30.141879zM905.853883 84.397261q26.122962-3.014188 36.170255 6.53074t8.037834 34.663161-5.526011 56.767205-7.53547 64.805039-8.037834 65.809769-7.033105 59.781393q-3.014188 29.137149-16.578033 37.174984t-30.644243-10.047293q-10.047293-10.047293-26.122962-24.615868t-27.127691-25.620597q-12.056752-11.052022-22.104044-7.53547t-23.108774 16.578033q-27.127691 27.127691-47.724641 49.231735t-48.729371 50.236465q-6.028376 6.028376-14.06621 11.554387t-17.080398 8.037834-19.089857-0.502365-20.094586-14.06621q-6.028376-6.028376-10.549658-10.047293t-8.540199-8.037834-8.540199-8.037834-11.554387-12.056752q-20.094586-20.094586-16.075669-35.165525t25.118232-35.165525l38.179713-40.189172q19.089857-20.094586 45.212818-46.217547 19.089857-18.085127 26.122962-32.151337t-7.033105-26.122962q-9.042564-9.042564-23.108774-24.615868t-24.113503-25.620597q-17.080398-17.080398-13.061481-30.141879t30.141879-16.075669 58.776664-7.033105 67.316863-7.53547 67.819227-7.53547 60.283758-7.033105zM350.238584 640.012559q6.028376 6.028376 10.549658 10.047293t8.540199 8.037834l8.037834 9.042564 12.056752 11.052022q20.094586 20.094586 17.582763 36.672619t-23.611138 37.677348q-19.089857 19.089857-40.189172 40.691536t-47.222277 47.724641q-18.085127 18.085127-22.606409 29.639514t8.540199 24.615868q10.047293 9.042564 22.606409 22.606409t22.606409 23.611138q17.080398 17.080398 12.559116 30.141879t-30.644243 16.075669-58.274299 7.033105-66.814498 8.037834-68.321592 8.037834-60.786122 7.033105q-25.118232 2.009459-35.66789-7.53547t-8.540199-33.658431q2.009459-25.118232 5.526011-56.767205t7.53547-64.805039 8.037834-65.809769 7.033105-59.781393q3.014188-30.141879 16.578033-37.677348t30.644243 9.544928q10.047293 10.047293 27.630056 26.122962t28.634785 27.127691q12.056752 12.056752 20.094586 10.549658t20.094586-14.568575q13.061481-13.061481 25.118232-25.620597t24.113503-24.615868 24.615868-25.118232 26.625326-27.127691q6.028376-6.028376 13.061481-12.056752t15.573304-9.042564 18.085127-0.502365 20.59695 13.563845z" p-id="2124" fill="#ffffff"></path></svg> -->
          <div class="w-100 h-100 overflow-hidden">
            <!-- <iframe class="w-100 h-100" :src="videoSrc" frameborder="0" allow="fullscreen" scrolling="no" controls>
            </iframe> -->
            <!-- <video class="w-100 h-100" :src="videoSrc" controls="false" autoplay muted @ended="onEnded('video1')"
              ref="video1">
              您的浏览器不支持 video 标签。
            </video> -->
            <div id="video-container" ></div>
          </div>
        </Item>
      </aside>
      <aside class="common-box w-50 h-100 pt-4 border-box">
        <Item title="设备监控">
          <!-- <svg t="1642341515961" @click="onViewVideo(videoSrc)" class="large-icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2123" width="1.6rem" height="1.6rem"><path d="M919.920093 725.414549q3.014188 26.122962 7.033105 58.776664t7.53547 66.814498 7.53547 67.819227 7.033105 60.786122q6.028376 47.222277-41.193901 44.208089-25.118232-2.009459-56.767205-5.526011t-64.805039-7.53547-65.809769-8.037834-59.781393-7.033105q-29.137149-3.014188-37.174984-16.578033t9.042564-30.644243q11.052022-10.047293 27.127691-27.630056t27.127691-28.634785q11.052022-12.056752 7.033105-22.104044t-16.075669-23.108774q-28.13242-27.127691-51.241194-49.231735t-51.241194-51.241194q-6.028376-6.028376-12.056752-13.061481t-9.042564-15.573304-1.004729-18.085127 13.061481-20.59695q6.028376-6.028376 10.047293-10.549658t8.037834-8.037834 8.540199-8.037834 11.554387-12.559116q20.094586-20.094586 37.174984-17.080398t37.174984 23.108774 41.193901 40.691536 47.222277 46.719912q19.089857 18.085127 32.653702 25.118232t26.625326-6.028376q9.042564-9.042564 22.606409-21.60168t23.611138-22.606409q17.080398-17.080398 30.644243-13.061481t16.578033 30.141879zM43.79615 383.80659q-3.014188-26.122962-7.033105-58.776664t-7.53547-66.814498-7.53547-67.819227-7.033105-60.786122q-3.014188-26.122962 6.53074-36.170255t33.658431-8.037834q25.118232 2.009459 56.767205 5.526011t64.805039 7.53547 65.809769 8.037834 59.781393 7.033105q30.141879 3.014188 37.677348 16.578033t-9.544928 30.644243q-10.047293 10.047293-24.615868 26.122962t-25.620597 27.127691q-12.056752 12.056752-8.037834 22.104044t17.080398 23.108774q13.061481 14.06621 24.615868 24.615868t22.606409 21.099315 23.108774 22.606409l25.118232 25.118232q6.028376 6.028376 11.554387 14.06621t8.037834 17.080398-0.502365 19.089857-13.061481 20.094586l-11.052022 11.052022q-4.018917 4.018917-7.53547 8.037834t-8.540199 8.037834l-11.052022 12.056752q-20.094586 20.094586-34.663161 15.070939t-34.663161-25.118232-38.179713-37.677348-44.208089-43.705724q-18.085127-18.085127-32.151337-25.118232t-27.127691 6.028376q-9.042564 10.047293-25.118232 24.615868t-26.122962 24.615868q-17.080398 17.080398-30.141879 13.061481t-16.075669-30.141879zM905.853883 84.397261q26.122962-3.014188 36.170255 6.53074t8.037834 34.663161-5.526011 56.767205-7.53547 64.805039-8.037834 65.809769-7.033105 59.781393q-3.014188 29.137149-16.578033 37.174984t-30.644243-10.047293q-10.047293-10.047293-26.122962-24.615868t-27.127691-25.620597q-12.056752-11.052022-22.104044-7.53547t-23.108774 16.578033q-27.127691 27.127691-47.724641 49.231735t-48.729371 50.236465q-6.028376 6.028376-14.06621 11.554387t-17.080398 8.037834-19.089857-0.502365-20.094586-14.06621q-6.028376-6.028376-10.549658-10.047293t-8.540199-8.037834-8.540199-8.037834-11.554387-12.056752q-20.094586-20.094586-16.075669-35.165525t25.118232-35.165525l38.179713-40.189172q19.089857-20.094586 45.212818-46.217547 19.089857-18.085127 26.122962-32.151337t-7.033105-26.122962q-9.042564-9.042564-23.108774-24.615868t-24.113503-25.620597q-17.080398-17.080398-13.061481-30.141879t30.141879-16.075669 58.776664-7.033105 67.316863-7.53547 67.819227-7.53547 60.283758-7.033105zM350.238584 640.012559q6.028376 6.028376 10.549658 10.047293t8.540199 8.037834l8.037834 9.042564 12.056752 11.052022q20.094586 20.094586 17.582763 36.672619t-23.611138 37.677348q-19.089857 19.089857-40.189172 40.691536t-47.222277 47.724641q-18.085127 18.085127-22.606409 29.639514t8.540199 24.615868q10.047293 9.042564 22.606409 22.606409t22.606409 23.611138q17.080398 17.080398 12.559116 30.141879t-30.644243 16.075669-58.274299 7.033105-66.814498 8.037834-68.321592 8.037834-60.786122 7.033105q-25.118232 2.009459-35.66789-7.53547t-8.540199-33.658431q2.009459-25.118232 5.526011-56.767205t7.53547-64.805039 8.037834-65.809769 7.033105-59.781393q3.014188-30.141879 16.578033-37.677348t30.644243 9.544928q10.047293 10.047293 27.630056 26.122962t28.634785 27.127691q12.056752 12.056752 20.094586 10.549658t20.094586-14.568575q13.061481-13.061481 25.118232-25.620597t24.113503-24.615868 24.615868-25.118232 26.625326-27.127691q6.028376-6.028376 13.061481-12.056752t15.573304-9.042564 18.085127-0.502365 20.59695 13.563845z" p-id="2124" fill="#ffffff"></path></svg> -->
          <div class="w-100 h-100 overflow-hidden">
            <!-- <embed class="w-100 h-100" :src="videoSrc" type="video/mp4">  -->
            <!-- <iframe class="w-100 h-100" :src="videoSrc2" frameborder="0" allow="autoplay" scrolling="no"></iframe> -->
            <div id="video-container1" ></div>
          </div>
        </Item>
      </aside>
    </section>
    <aside class="common-box position-absolute top-0 right-box">
      <div class="h-100 border-box">
        <Item :title="shebeiname">
          <!-- 水质设备数据框开始 -->
          <div class="flex-vertical" v-show="waterData">
            <div class="flex-header">
              <div class="flex-item">
                <Des class="flex-content mr-4" title="温度 °C" :content="TEM" />
                <Des class="flex-content" title="PH" :content="PH" />
              </div>
              <div class="flex-item mt-2">
                <Des class="flex-content mr-4" title="含氧量" :content="O2" />
                <Des class="flex-content" title="电导" :content="CON" />
              </div>
            </div>
            <div class="line-box flex-content">
              <el-tabs type="card" v-model="active">
                <el-tab-pane label="水温" name="0" />
                <el-tab-pane label="含氧量" name="1" />
                <el-tab-pane label="PH" name="2" />
              </el-tabs>
              <div class="w-100 h-100 info-line"></div>
            </div>
          </div>
          <!-- 气象数据框开始 -->
          <div class="flex-vertical" v-show="weatherData">
            <div class="flex-header">
              <div class="flex-item">
                <Des class="flex-content mr-4" title="温度 °C" :content="TEM" />
                <Des class="flex-content" title="湿度" :content="HUMIDITY" />
              </div>
              <div class="flex-item mt-2">
                <Des class="flex-content mr-4" title="气压" :content="ATM" />
                <Des class="flex-content" title="雨量" :content="RAINFALL" />
              </div>
            </div>
            <div class="line-box flex-content">
              <el-tabs type="card" v-model="active2">
                <el-tab-pane label="温度" name="0" />
                <el-tab-pane label="湿度" name="3" />
                <el-tab-pane label="气压" name="4" />
                <el-tab-pane label="雨量" name="5" />
              </el-tabs>
              <div class="w-100 h-100 info-line"></div>
            </div>
          </div>
          <!-- 气象数据框结束 -->
        </Item>
      </div>
     
    </aside>

    <div v-if="box === 1" class="equip-box">
      <img src="@/assets/img/frame.png" alt="">
      <div class="text" style="white-space: pre-wrap;">
        <div class="erow">
          <span>设备名称： <span class="underline">{{ miaoshu.EQUIPMENT_NAME }}</span></span>
        </div>
        <div class="erow">
          <span>采集时间： <span class="underline">{{ miaoshu.ACQUISITION_TIME }}</span></span>
        </div>

        <div class="erow">
          <span>设备描述： <span class="underline">{{ miaoshu.INTRODUCE }}</span> </span>
        </div>

        <div class="erow">
          <span class="rowh"><span class="item"> 电压：</span> <span class="underline1">{{ miaoshu.ACQUISITION_TIME }}</span>
          </span>
          <span class="rowh"><span class="item"> 状态： </span> <span class="underline1">{{ miaoshu.state }}</span> </span>
        </div>

        <div v-if="miaoshu.type == 1" class="erow">
          <span class="rowh"><span class="item"> ORP： </span> <span class="underline1">{{ miaoshu.ORP }}</span> </span>
          <span class="rowh"><span class="item"> 电导： </span> <span class="underline1">{{ miaoshu.CON }}</span> </span>
        </div>
        <!-- ：温度，ph，含氧量，盐度 -->
        <div v-if="miaoshu.type == 1" class="erow">
          <span class="rowh"><span class="item"> 温度： </span> <span class="underline1">{{ miaoshu.TEM }}</span> </span>
          <span class="rowh"><span class="item"> PH： </span> <span class="underline1">{{ miaoshu.PH }}</span> </span>
        </div>
        <div v-if="miaoshu.type == 1" class="erow">
          <span class="rowh"><span class="item"> 含氧量： </span> <span class="underline1">{{ miaoshu.O2 }}</span> </span>
          <span class="rowh"><span class="item"> 盐度：</span> <span class="underline1">{{ miaoshu.SALT }}</span> </span>
        </div>


        <div v-if="miaoshu.type == 2" class="erow">
          <span class="rowh"><span class="item"> 光照：</span> <span class="underline1">{{ miaoshu.LIGHT }}</span> </span>
          <span class="rowh"><span class="item"> 风速：</span> <span class="underline1">{{ miaoshu.WIND_SPEED }}</span>
          </span>
        </div>

        <div v-if="miaoshu.type == 2" class="erow">
          <span class="rowh"><span class="item"> 风向：</span> <span class="underline1">{{ miaoshu.WIND_DIRECTION }}</span>
          </span>
          <span class="rowh"><span class="item"> 辐射量： </span> <span class="underline1">{{ miaoshu.RADIATION }}</span>
          </span>
        </div>

        <!-- 温度，湿度，气压，雨量 -->
        <div v-if="miaoshu.type == 2" class="erow">
          <span class="rowh"><span class="item"> 温度：</span> <span class="underline1">{{ miaoshu.TEM }}</span>
          </span>
          <span class="rowh"><span class="item"> 湿度： </span> <span class="underline1">{{ miaoshu.HUMIDITY }}</span>
          </span>
        </div>
        <div v-if="miaoshu.type == 2" class="erow">
          <span class="rowh"><span class="item"> 气压：</span> <span class="underline1">{{ miaoshu.ATM }}</span>
          </span>
          <span class="rowh"><span class="item"> 雨量： </span> <span class="underline1">{{ miaoshu.RAINFALL }}</span>
          </span>
        </div>


      </div>
      <div class="close-icon d-flex align-items-center justify-content-center" @click="onCloseCover">
        <svg t="1639825855503" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
          p-id="26567" width="1rem" height="1rem">
          <path
            d="M846.005097 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519L96.632851 254.131955c-43.644009-43.644009-43.644009-114.348328 0-157.992337s114.348328-43.644009 157.992337 0L925.001265 766.515694c43.644009 43.644009 43.644009 114.348328 0 157.992337C903.17926 946.330036 874.592179 957.24155 846.005097 957.24155z"
            p-id="26568" fill="#ffffff"></path>
          <path
            d="M175.62902 957.24155c-28.587082 0-57.174164-10.911514-78.996169-32.733519-43.644009-43.644009-43.644009-114.348328 0-157.992337L767.008928 96.139617c43.644009-43.644009 114.348328-43.644009 157.992337 0s43.644009 114.348328 0 157.992337L254.625188 924.508032C232.803183 946.330036 204.216101 957.24155 175.62902 957.24155z"
            p-id="26569" fill="#ffffff"></path>
        </svg>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import axios from 'axios';
import EZUIKit from 'ezuikit-js';
import { shebeilistv2, caicon, select_equipmentv2 } from '@/request/api';
import { Collapse, CollapseItem } from 'vant';
// import { videoPlayer } from 'vue-video-player'
// import 'video.js/dist/video-js.css'
import Vue from 'vue';
Vue.use(Collapse);
Vue.use(CollapseItem);

// 在script开始处添加
import shuizhiIcon from '@/assets/img/shuizhi.svg'
import location2Icon from '@/assets/img/location2.svg'





export default {
  name: 'Home',
  // components: { videoPlayer },
  data() {
    return {
      deviceDetail: {  
        value: {  
          deviceSerial: 'BD4573640',
          deviceSerial1: 'BD7417918'  
        }  
      },  
      jktokens: {  
        value: ''  
      },  
      player: null,
      player1: null,
      base_id:"",
      waterData: true,//默认展示水质设备信息
      weatherData: false,//展示气象数据界面
      isShow: false,
      isShow1: false,
      activeNames: ['1'],
      alertYuanList: [],
      a: 0,
      b: 0,
      c: 0,
      aa: {
        x: ['10:10', '10:20', '10:30', '10:40', '10:50', '11:00', '11:10', '11:20', '11:30', '11:40'],
        y: [
          {
            name: '水温值',
            type: 'solid',
            color: '#3c78d8',
            data: []
          },
          {
            name: '水温上限值',
            type: 'dotted',
            color: '#eb0e2e',
            data: []
          },
          {
            name: '水温下限值',
            type: 'dotted',
            color: '#0bf00b',
            data: []
          }
        ]
      },
      tianqi: { home: 1 },
      shuju: {
        templist: [[0], [0], [0], [0]]
      },
      // tianqi: {},
      active: '0',
      active2: '0',
      box: 0,
      map: '',
      shebeiname: '设备参数 水质检测设备一',
      equipmentID: '',
      chuangan2: [],
      chuangan: '',
      TEM: '',
      PH: '',
      O2: '',
      SALT: '',
      CON: '',
      RAINFALL: '',
      ATM: '',
      HUMIDITY: '',
      X: '',
      Y: '',
      shelist: [],
      zuobiaolist: [],
      base_name: "",

      miaoshu: { EQUIPMENT_NAME: '', INTRODUCE: '', Online_Voltage: '', state: '', LIGHT: '', WIND_SPEED: '', WIND_DIRECTION: '', RADIATION: '', ORP: '', CON: '', type: 1 },
      inforlist: [],
      alertlist: [],
      templist: [],


      dialogVisible: false,
      dialogVideo: '',

      // 视频播放器配置

    }

  },
  computed: {
    ...mapGetters(['getBaseId']),
    Base_id() {
      return this.getBaseId;
    },
    Base_name() {
      return this.$route.query.base_name || '未知基地';
    },
  },
  mounted() {
    this.renderMap();
    this.renderLine(0);
    this.getlist();
    this.getToken();
    if (this.$route.query.base_id) {
      this.updateBaseId(parseInt(this.$route.query.base_id, 10));
    }
  },
  watch: {
    active() {
      if (this.shuju == undefined || this.shuju.templist[0][0] == 0) {
        this.aa = this.shuju2.templist[this.active];  //初始折线图数据
      } else {
        this.aa = this.shuju.templist[this.active];
      }
      this.renderLine(0);
    },
    active2() {
      if (this.shuju == undefined || this.shuju.templist[0][0] == 0) {
        this.aa = this.shuju2.templist[this.active];  //初始折线图数据
      } else {
        this.aa = this.shuju.templist[this.active2];
      }
      this.renderLine(1);
    },
  },
  methods: {
    ...mapActions(['updateBaseId']),
    initPlayer() {  
      if (!this.deviceDetail.value.deviceSerial || !this.jktokens.value) {  
        console.error('设备序列号或访问令牌未设置');  
        return;  
      }  
      this.player = new EZUIKit.EZUIKitPlayer({  
        id: 'video-container',  
        accessToken: this.jktokens.value,  
        url: `ezopen://open.ys7.com/${this.deviceDetail.value.deviceSerial}/1.live`,  
        height: '220',  
        width:'450',
        template: 'simple',  
        // header: ['capturePicture', 'save', 'zoom'],  
        // footer: ['hd', 'fullScreen']  
      });  
    },  
    initPlayer1() {  
      if (!this.deviceDetail.value.deviceSerial1 || !this.jktokens.value) {  
        console.error('设备序列号或访问令牌未设置');  
        return;  
      }  
      this.player1 = new EZUIKit.EZUIKitPlayer({  
        id: 'video-container1',  
        accessToken: this.jktokens.value,  
        url: `ezopen://open.ys7.com/${this.deviceDetail.value.deviceSerial1}/1.live`,  
        height: '220',  
        width:'450',
        template: 'simple',  
        // header: ['capturePicture', 'save', 'zoom'],  
        // footer: ['hd', 'fullScreen']  
      });  
    },  
    async getToken() {  
      try {  
        const appKey = '3d0be5dc16b846e58ba2e4efb80d6d7f'; // 替换为你的appKey  
        const appSecret = '1d040ec6b1a4d12061fa97ef21987942'; // 替换为你的appSecret  
  
        const response = await axios.post('https://open.ys7.com/api/lapp/token/get',  
          `appKey=${appKey}&appSecret=${appSecret}`,  
          {  
            headers: {  
              'Content-Type': 'application/x-www-form-urlencoded'  
            }  
          }  
        );  
        // 处理响应数据  
        this.jktokens.value=response.data.data.accessToken;
        console.log('shuju:', response.data.data.accessToken); // 这里你应该会看到返回的token数据（如果请求成功的话）  
      //  this.jktokens=response.data.accessToken;
        this.initPlayer();
       this.initPlayer1();
      } catch (error) {  
        // 错误处理  
        console.error('获取Token失败:', error);  
      }  
    } ,
    onEnded(v) {
      if (v == 'video1')
        this.$refs.video1.play()
      else
        this.$refs.video2.play()
      // this.$emit('ended')
    },
    showToggle(ind) {
      if (ind == 1) {
        this.isShow = !this.isShow
      } else {
        this.isShow1 = !this.isShow1
      }
    },
    onViewVideo(src) {
      this.dialogVideo = src;
      this.dialogVisible = true;
    },
    linkhome() {
      this.$router.push({
        path: '/home'
      });
    },

    linkyangzhi() {
      this.$router.push({
        path: '/cultiver',
        query: {
          base_id: this.base_id,
          base_name: this.$route.query.base_name
        }
      });

    },
    linkhistory() {
      this.$router.push({
        path: '/data/history/',
        query: {
          base_id: this.base_id,
          base_name: this.$route.query.base_name
        }
      });

    },
    addAlarm(message) {
      this.alertYuanList.push({
        message,
        time: this.getCurrentTime(),
      });
    },
    getCurrentTime() {
      const now = new Date();
      return `${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()} ${now.getHours()}:${now.getMinutes()}:${now.getSeconds()}`;
    },
    getlist() {
      this.base_name = this.$route.query.base_name;
      this.base_id=this.$route.query.base_id
      shebeilistv2({
        base_id: this.base_id,
      }).then(res => {
        this.shelist = res.data.shebeilist;
        for (let i = 1; i < this.shelist.length; i++) {
          const device = this.shelist[i];
          const Online_Voltage = device.Online_Voltage;    
          // 检查设备类型是否为水质设备
          if (device.EQUIPMENT_TYPE === '水质检测') {
            const oxygenLevel = device.O2;
            const temperature = device.TEM;
            const pHValue = device.PH;
            const UANDL = device.UANDL;
            // console.log('UNAND', UANDL);
            let parsedUANDL = null;
            try {
              parsedUANDL = JSON.parse(UANDL);  // 将 UANDL 解析为对象
              
            } catch (error) {
              console.error('解析 UANDL 失败:', error);
            }

            //console.log('UNAND', parsedUANDL);
            //console.log('UNAND', parsedUANDL.ph.co[1]);
            if (oxygenLevel < parsedUANDL.o2.th[0] || oxygenLevel > parsedUANDL.o2.th[1]) {
              const alarmMessage = `${device.EQUIPMENT_NAME}的溶氧值异常,当前值为${oxygenLevel}`;
              this.addAlarm(alarmMessage);
            }

            if (temperature < parsedUANDL.te.th[0] || temperature > parsedUANDL.te.th[1]) {
              const alarmMessage = `${device.EQUIPMENT_NAME}的温度异常,当前值为${temperature}`;
              this.addAlarm(alarmMessage);
            }

            if (pHValue < parsedUANDL.ph.th[0] || pHValue > parsedUANDL.ph.th[1]) {
              const alarmMessage = `${device.EQUIPMENT_NAME}的PH值异常,当前值为${pHValue}`;
              this.addAlarm(alarmMessage);
            }
            if (Online_Voltage == 0 || Online_Voltage < 10) {
              const alarmMessage = `${device.EQUIPMENT_NAME}的电压值异常,当前值为${Online_Voltage}`;
              this.addAlarm(alarmMessage);
            }
          }
        }
        this.zuobiaolist = res.data.zuobiaoilist;
        this.inforlist = res.data.inforlist;
        this.alertlist = res.data.alertlist;
        this.tianqi = res.data.tianqi;
        this.chuangan2 = res.data.chuangan2; //所有设备的传感器参数（最新的一条）
        this.chuangan = res.data.chuangan;  //传感器传来的设备一参数,用来初始化
        this.PH = this.chuangan.PH[0]
        this.TEM = this.chuangan.TEM[0]   //初始温度
        this.O2 = this.chuangan.O2[0]
        this.SALT = this.chuangan.SALT[0]
        this.CON = this.chuangan.CON[0]
        // this.shebeiname="设备参数  " +  this.chuangan.EQUIPMENT_NAME

        // this.shuju = res.data.shuju;
        this.shuju2 = res.data.shuju2;
        this.aa = this.shuju2.templist[0];//默认折线图、设备一参数 
        this.renderMap();
        this.renderLine(0);
        this.selectEquipment(this.shelist[0], 1) //默认选择第一个设备展示
        // console.log("res");
        // console.log(res)
      })
    },
    //  设备参数
    selectEquipment(i, index) {
      this.equipmentID = i.ID
      // console.log(this.chuangan2,i.ID)
      if (i.EQUIPMENT_TYPE == '水质检测') {
        this.weatherData = false;
        this.waterData = true;
        this.shebeiname = "设备参数  " + i.EQUIPMENT_NAME;
        this.PH = i.PH
        this.TEM = i.TEM
        this.O2 = i.O2
        this.SALT = i.SALT
        this.CON = i.CON
        this.aa = ''
        // this.renderLine(0);

        // for (this.chuanganProp of this.chuangan2) {       //遍历传感器传来的设备参数
        //   if (this.chuanganProp.EQUIPMENT_id == i.ID) {   //通过设备号是否相等确定点击的设备
        //     // this.chuangan =this.chuanganProp;
        //     this.PH = this.chuanganProp.PH
        //     this.TEM = this.chuanganProp.TEM
        //     this.O2 = this.chuanganProp.O2
        //     this.SALT = this.chuanganProp.SALT
        //     this.shebeiname = "设备参数  " + i.EQUIPMENT_NAME;

        //   }
        // }
      } else if (i.EQUIPMENT_TYPE == '气象检测') {
        this.weatherData = true;
        this.waterData = false;
        this.shebeiname = "设备参数  " + i.EQUIPMENT_NAME;
        this.HUMIDITY = i.HUMIDITY
        this.TEM = i.TEM
        this.RAINFALL = i.RAINFALL
        this.ATM = i.ATM
        this.aa = ''
        // this.renderLine(1);
      }
      //  点击设备设置中心位置
      for (this.zuoBiaoProp of this.zuobiaolist) {
        this.X = this.zuoBiaoProp[0];
        this.Y = this.zuoBiaoProp[1];
        const zuoBiaoStr = this.zuoBiaoProp.toString();
        if (zuoBiaoStr == i.EQUIPMENT_LOCATION) {
          // console.log([this.zuoBiaoProp[0], this.zuoBiaoProp[1]])
          this.map.setCenter([this.zuoBiaoProp[0], this.zuoBiaoProp[1]]);
        }
      }
    },
    // 传送设备号,获得对应设备号的传感器的15条数据
    addEquipment(ty) {
      select_equipmentv2({
        base_id: this.$route.query.base_id,
        equipmentID: this.equipmentID
      }).then(res => {
        this.shuju = res.data.shuju;
        this.aa = this.shuju.templist[this.active];
        this.renderLine(ty);
      })
    },
    renderMap() {
      this.$AMapLoader.load({//这里设置了高德地图JavaScript API的密钥，以便在加载API时进行身份验证。
        key: 'bb41d02b6376f70646e2490b6bf5f80b',
        version: '1.4.15',//这里指定了要加载的高德地图JavaScript API的版本号
        plugins: [],
        AMapUI: {
          version: '1.1',
          plugins: []
        },
        Loca: {
          version: '1.3.2'
        }
      }).then((AMap) => {//使用Promise的.then()方法，当高德地图API加载完成后执行回调函数。
        this.map = new AMap.Map('map-container', {
          //在回调函数中，创建了一个新的高德地图实例并将其绑定到Vue组件的this.map属性上。这里指定了地图容器的ID为'map-container'，
          //地图的样式为蓝色。layers属性设置了两个图层，一个是卫星图层，一个是道路图层。
          //zoom属性指定了地图的缩放级别，center属性设置了地图的中心位置，该位置从this.zuobiaolist数组中获取第一个坐标点的位置。
          mapStyle: 'amap://styles/blue',
          layers: [new AMap.TileLayer.Satellite(), new AMap.TileLayer.RoadNet()],
          zoom: 18,
          // 初始地图中心位置
          center: this.zuobiaolist[0]
        });
        //代码的第一行初始化了一个名为 list 的常量变量，其值为 this.zuobiaolist。
        const list = this.zuobiaolist;
        async function ca_icon(local, base_id) {
          let icon = '';
          //ca_icon 函数使用 await 关键字等待 caicon 函数返回的 Promise 解决。caicon 函数可能是一个异步函数，接受一个包含两个属性 zuobiao 和 base_id 的对象。
          await caicon({ zuobiao: local, base_id: base_id }).then(res => {
            //当 Promise 解决后，调用 then 方法，并传入一个回调函数，回调函数接受一个名为 res 的参数。
            if (res && res.data == "水质检测") {
              // if (res && res.data.EQUIPMENT_TYPE == "水质检测") {
              icon = new AMap.Icon({
                size: new AMap.Size(40, 50), // 图标尺寸
                image: shuizhiIcon,
                imageSize: new AMap.Size(40, 50)
              });
            } else {
              icon = new AMap.Icon({
                size: new AMap.Size(40, 50), // 图标尺寸
                image: location2Icon,
                imageSize: new AMap.Size(40, 50),
              });
            }
          })
          return icon;
        }
        const shelist = this.shelist
        // console.log('annnnnnnnnn');
        // console.log(shelist);
        //const list = this.zuobiaolist;
        list.forEach(async (item, index) => { //坐标循环，以列表的形式赋值给item
          let icon = await ca_icon(item, this.base_id)//await后面接promise对象才有用
          const marker = new AMap.Marker({
            position: item,
            offset: new AMap.Pixel(-20, -25), // 偏移值
            icon,
            label: {
              content: shelist[index].EQUIPMENT_NAME,
              //offset: new AMap.Pixel(27, 25)

            },
          })

          marker.on('click', () => {
            this.box = 1

            this.map.setCenter([item[0], item[1]]);
            // console.log([item[0], item[1]])
            const zuobiaopinStr = item.toString()
            for (this.abc of this.shelist) {
              if (zuobiaopinStr == this.abc.EQUIPMENT_LOCATION) {  //判断地图点坐标与设备列表坐标，确定设备号
                // 设备描述
                this.selectEquipment(this.abc, 1)
                // console.log("________________")
                // console.log(this.abc)

                var MOB = (this.abc.MOBILITY > 0) ? "可移动" : "不可移动"
                var state = (this.abc.STATE == 0) ? "停止" : "正常"
                // var ORP=(this.abc.ORP == null) ? "" : this.abc.ORP
                // var CON=(this.abc.CON == null) ? "" : this.abc.CON
                // console.log(this.abc)
                this.miaoshu = this.abc
                // console.log('annnnnnnnnn2222222222');
                // console.log(this.miaoshu);
                // ACQUISITION_TIME: "2022-10-09 03:23:39"
                //this.abc打印出来是这样
                // ATM: ""
                // BACILITIES_NAME: "裕安养殖塘二"
                // BACILITIES_id: 6
                // CARD_NUMBER: 11
                // CON: ""
                // CONFIG: "发送下位机配置，如采集或上传时间间隔"
                // CREATE_TIME: "2022-03-30T20:35:18"
                // EQUIPMENT_LOCATION: "121.835,31.5755"
                // EQUIPMENT_NAME: "水质检测设备7"
                // EQUIPMENT_TYPE: "水质检测"
                // HUMIDITY: ""
                // ID: 17
                // INTRODUCE: "二合一基础水质，太阳能供电。"
                // LIGHT: ""
                // MOBILITY: 0
                // O2: "0.41"
                // ORP: "436.00"
                // Online_Voltage: "12.14"
                // PH: "7.81"
                // RADIATION: ""
                // RAINFALL: ""
                // SALT: "0.00"
                // STATE: 1
                // TEM: "20.16"
                // UANDL: "{\"te\": {\"co\": [-5,50], \"th\": [0,35]}, \"o2\": {\"co\": [0,15], \"th\": [4,11]},\"ph\":{\"co\":[0,14],\"th\":[5,9]}, \"salt\": {\"co\": [0,97], \"th\": [0,47]},\"con\": {\"co\": [50,1000], \"th\": [50,500]}}"
                // UPDATE_TIME: "2022-03-30T20:35:21"
                // VOLTAGE: "222.00"
                // WIND_DIRECTION: ""
                // WIND_SPEED: ""
                // state: "正常"
                // type: 1

                // this.miaoshu = "设备名称:" + this.abc.EQUIPMENT_NAME + "\n\n设备描述：" + this.abc.INTRODUCE + "\n\n电压:" + this.abc.Online_Voltage + "\n\n运行状态：" + state + "\n\n移动性：" + MOB
                if (this.abc.EQUIPMENT_TYPE == '水质检测') {
                  if (this.abc.SPECIAL_id != 0) {
                    this.miaoshu.SPECIAL_id = 1
                  }
                  // this.miaoshu.EQUIPMENT_NAME = this.abc.EQUIPMENT_NAME
                  // this.miaoshu.INTRODUCE = this.abc.INTRODUCE
                  // this.miaoshu.Online_Voltage = this.abc.Online_Voltage
                  this.miaoshu.state = state
                  // this.miaoshu.ORP = this.abc.ORP
                  // this.miaoshu.CON = this.abc.CON
                  this.miaoshu.type = 1
                  this.addEquipment(0)




                  //温度，ph，含氧量，盐度

                  // this.miaoshu = "设备名称:" + this.abc.EQUIPMENT_NAME + "\n\n设备描述：" + this.abc.INTRODUCE + "\n\n电压:" + this.abc.Online_Voltage + 
                  //"\t\t运行状态：" + state + "\n\nORP：" + this.abc.ORP+"\t\t电导："+this.abc.CON

                  // this.miaoshu="设备名称:" + this.abc.EQUIPMENT_NAME + "\n\n设备描述：" + this.abc.INTRODUCE + "\n\n<table><tr><td>电压</td><td>"+this.abc.Online_Voltage+"</td><td>运行状态</td><td>"+state+"</td></tr><tr><td>ORP</td><td>"+this.abc.ORP+"</td><td>电导</td><td>"+this.abc.CON+"</td></tr></table>"

                  // this.miaoshu="设备名称:" + this.abc.EQUIPMENT_NAME + "\n\n设备描述：" + this.abc.INTRODUCE + "\n\n<div style='display: flex;'><span style='width:200px'>电压："+this.abc.Online_Voltage+"</span><span style='width:200px'>运行状态："+state+"</div><div style='display: flex;'><span style='width:200px'>ORP："+this.abc.ORP+"</span><span style='width:200px'>电导："+this.abc.CON+"</span></div>"

                  // this.miaoshu = "设备名称:" + this.abc.EQUIPMENT_NAME + "\n\n设备描述：" + this.abc.INTRODUCE + "\n\n电压:" + this.abc.Online_Voltage + "\t\t运行状态：" + state + "\n\nORP：" + this.abc.ORP+"\t\t电导："+this.abc.CON
                }

                if (this.abc.EQUIPMENT_TYPE == '气象检测') {
                  //this.miaoshu = "设备名称:" + this.abc.EQUIPMENT_NAME + "\n\n设备描述：" + this.abc.INTRODUCE + "\n\n电压:" + this.abc.Online_Voltage + "\t\t\t\t运行状态：" + state + 
                  //"\n\n光照：" + this.abc.LIGHT+"\t\t\t\t风速："+this.abc.WIND_SPEED+"\n\n风向:" + this.abc.WIND_DIRECTION+"\t\t\t\t辐射量："+this.abc.RADIATION
                  // this.miaoshu.EQUIPMENT_NAME = this.abc.EQUIPMENT_NAME
                  // this.miaoshu.INTRODUCE = this.abc.INTRODUCE
                  // this.miaoshu.Online_Voltage = this.abc.Online_Voltage
                  // this.miaoshu.state = state
                  // this.miaoshu.LIGHT = this.abc.LIGHT
                  // this.miaoshu.WIND_SPEED = this.abc.WIND_SPEED
                  // this.miaoshu.WIND_DIRECTION = this.abc.WIND_DIRECTION
                  // this.miaoshu.RADIATION = this.abc.RADIATION
                  this.miaoshu.type = 2

                  this.addEquipment(1)
                  //温度，湿度，气压，雨量

                }
                //                 设备名： app1_equipment中的EQUIPMENT_NAME
                // 设备描述：app1_equipment中的INTRODUCE
                // 电压：水质设备app1_sensing_data中的VOLTAGE，气象设备app1_weather_data中的VOLTAGE
                // 运行状态：正常和停止两种，app1_equipment中的STATE ,0表示停止，1表示正常
                // 移动性：  app1_equipment中的MOBILITY，0表示不可移动，1表示可移动
                // console.log(this.miaoshu)

              }
            }

          })
          this.map.add(marker);
        })
      })
    },
    // renderMap_bak () {
    //   this.$AMapLoader.load({
    //     key: 'bb41d02b6376f70646e2490b6bf5f80b',
    //     version: '1.4.15',
    //     plugins: [],
    //     AMapUI: {
    //       version: '1.1',
    //       plugins: []
    //     },
    //     Loca: {
    //       version: '1.3.2'
    //     }
    //   }).then((AMap) => {
    //     this.map = new AMap.Map('map-container', {
    //       mapStyle: 'amap://styles/blue',
    //       zoom: 18,
    //       center: this.zuobiaolist[0]
    //     });
    //     const list = this.zuobiaolist;
    //     const markerList = [];
    //     list.forEach((item) => {
    //       const marker = new AMap.Marker({
    //         position: item
    //       })
    //       marker.on('click', () => {
    //         console.log(1, item);
    //         this.box = 1;
    //       })
    //       markerList.push(marker);
    //     })

    //     this.map.add(markerList);
    //   })
    // },
    renderLine(ind) {
      // val = {
      //   x: ['10:10', '10:20', '10:30', '10:40', '10:50', '11:00', '11:10', '11:20', '11:30', '11:40'],
      //   y: [
      //     {
      //       name: 'PH值',
      //       type: 'solid',
      //       data: [1, 2, 3, 5, 4, 8, 2, 6, 4, 10]
      //     },
      //     {
      //       name: 'PH上限值',
      //       type: 'dotted',
      //       data: [5, 5, 5, 5, 5, 5, 5, 5, 5, 5]
      //     },
      //     {
      //       name: 'PH下限值',
      //       type: 'dotted',
      //       data: [-5, -5, -5, -5, -5, -5, -5, -5, -5, -5]
      //     }
      //   ]
      // }
      // 初始折线图样式自定义ph数据
      const val = this.aa;
      const xData = val.x;
      const series = [];
      // 遍历初始ph数据
      val.y && val.y.forEach((item, index) => {
        if (index == 0) {
          series.push({
            type: 'line',
            smooth: true,
            symbol: 'circle',
            stack: '',
            symbolSize: 0,
            // areaStyle: {},
            areaStyle: {
              opacity: 0.8,
              color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgb(255, 191, 0)'
                },
                {
                  offset: 1,
                  color: 'rgba(224, 62, 76,0)'
                }
              ])
            },
            itemStyle: {
              normal: {
                lineStyle: {
                  width: 3,
                  color: "yellow",
                  type: item.type
                }
              }
            },
            emphasis: {
              focus: 'series'
            },
            data: item.data
          })

        } else if (index == 1) {
          series.push({
            type: 'line',
            smooth: true,
            stack: '',
            symbol: 'circle',
            symbolSize: 3,
            // areaStyle: {},
            // areaStyle: {
            //   opacity: 0.8,
            //   color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            //     {
            //       offset: 0,
            //       color: 'rgb(55, 162, 255)'
            //     },
            //     {
            //       offset: 1,
            //       color: 'rgba(116, 21, 219,0)'
            //     }
            //   ])
            // },

            // itemStyle: {
            //   normal: {
            //     lineStyle: {
            //       width: 3,
            //       // color: "rgb(0, 255, 255)",
            //       // color: item.color,//"blue",

            //       type: {

            //         type: [5, 5],

            //         dashOffset: 1
            //       }
            //     }
            //   }
            // },
            // emphasis: {
            //   focus: 'series'
            // },
            data: item.data
          })
        } else {
          series.push({
            type: 'line',
            smooth: true,
            stack: '',
            symbol: 'circle',
            symbolSize: 3,
            // areaStyle: {},
            // areaStyle: {
            //   opacity: 0.8,
            //   color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
            //     {
            //       offset: 0,
            //       color: 'rgb(128, 255, 165)'
            //     },
            //     {
            //       offset: 1,
            //       color: 'rgba(1, 191, 236,0)'
            //     },
            //     // {
            //     //   offset: 2,
            //     //   color: 'rgba(1, 191, 236,0)'
            //     // }
            //   ])
            // },

            // itemStyle: {
            //   normal: {
            //     lineStyle: {
            //       width: 3,
            //       // color: item.color,// "rgb(158,197,22)",
            //       type: {

            //         type: [5, 10],

            //         dashOffset: 5
            //       }
            //     }
            //   }
            // },
            // emphasis: {
            //   focus: 'series'
            // },
            data: item.data
          })
        }


      })
      const option = {
        legend: {
          show: true,
          top: '20%',
          textStyle: {
            color: '#fff'
          }
        },
        tooltip: {
          trigger: 'axis',
          padding: [10, 15],
          backgroundColor: 'rgba(0, 0, 0, 0.4)',
          textStyle: {
            color: '#fff'
          }
        },
        grid: {
          show: false,
          left: '3%',
          right: '4%',
          top: '30%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          // axisLine: {
          //   show: true
          // },
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#fff',
            interval: 0,
            rotate: 70
          },
          // splitLine: {
          //   show: true,
          // },
          data: xData || []
        },
        yAxis: {
          type: 'value',
          // axisLine: {
          //   show: true
          // },
          axisTick: {
            show: true //不显示坐标轴刻度线
          },



          axisLine: {
            show: true, //不显示坐标轴线
          },
          axisLabel: {
            show: true, //不显示坐标轴上的文字
            color: "#fff"
          },
          splitLine: {     //网格线
            "show": false
          }

          // splitLine: {
          //   show: true,
          //   // color:"#100f0e"
          // },
          // min: this.aa.min,
          // max: this.aa.max,

          // splitNumber: 10
        },
        // series: [
        //   {
        //     type: 'line',
        //     smooth: true,
        //     color: '#fd794c',
        //     symbol: 'circle',
        //     symbolSize: 3,
        //     lineStyle: {
        //       width: 3
        //     },
        //     data: this.templist
        //   },
        // ]
        series
      };
      const chart = this.$echarts.init(document.getElementsByClassName('info-line')[ind]);
      chart.setOption(option);
    },
    onCloseCover() {
      this.box = 0;
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.amap-marker-label {
  border: 0;
  background-color: transparent;
  padding-left: 20px;
  padding-top: 20px;
}

header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 7rem;
  background: url("~@/assets/img/head.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;

  .right-content {
    position: absolute;
    height: 7rem;
    right: 0;
    top: -0.2rem;
    padding-right: 5rem;
    line-height: 2rem;
  }

  .left-content {
    position: absolute;
    left: 1rem;
    top: 1.5rem;
    line-height: 2rem;
  }

  .bottom-box {
    position: absolute;
    top: 6.6rem;
    left: 50%;
    transform: translate(-50%, 0);
    width: calc(50% - 5rem);
  }
}

.information-title {
  cursor: pointer;
  /*鼠标悬停变小手*/
}

.page-wrapper {
  // background-image: url("~@/assets/img/bg.png");
  // background-size: 100% 100%;

  .map-container {
    opacity:1;
  }

  .header-box {
    padding-right: 20rem;
  }

  .left-box {
    left: 1rem;

    .area-list {
      li {
        line-height: 1.6rem;
      }
    }
  }

  .right-box {
    right: 1rem;

    .line-box {
      position: relative;

      /deep/.el-tabs {
        left: 0;
        top: 1.5rem;
        position: absolute;

        .el-tabs__item {
          color: #fff;

          &.is-active {
            background: #409eff;
          }
        }
      }
    }

    .error-list {
      line-height: 2rem;

      img {
        width: 4.5rem;
      }
    }
  }

  // 监控的位置
  .bottom-box {
    position: absolute;
    width: calc(50% - 4rem);
    height: 30%;
    bottom: 3rem;
    left: 50%;
    transform: translate(-50%, 0);
  }

  //  地图设备提示框样式
  .equip-box {
    position: absolute;
    top: 30%;
    left: 35%;

    img {
      width: 41rem;
      // height: auto;
    }

    .text {
      position: absolute;
      top: 10px;
      left: 30px;
      color: #fff;
      padding: 2.5rem 3.5rem;
      width: 40rem;

      .erow {
        display: flex;
        height: 40px;

        .rowh {
          width: 40%;

          .item {
            width: 30%;
            display: inline-block;
          }

          .underline1 {
            width: 45%;
            display: inline-block;

            border-style: hidden hidden dotted hidden;
            ;
          }
        }

        .underline {

          border-style: hidden hidden dotted hidden;
          ;
        }

      }
    }

    .close-icon {
      position: absolute;
      top: -2.5rem;
      right: 0rem;
      width: 2.4rem;
      height: 2.4rem;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 0.3rem;
    }
  }
}

.large-icon {
  position: absolute;
  top: 0.5rem;
  right: 1.5rem;
}

.dialog-video {
  width: 60rem;
  height: 30rem;
}
.area-list2 {
  li {
    line-height: 2.025rem;
  }
}

video::-webkit-media-controls-fullscreen-button {
  display: none;
}

video::-webkit-media-controls-play-button {
  display: none;
}

video::-webkit-media-controls-timeline {
  display: none;
}

video::-webkit-media-controls-current-time-display {
  display: none;
}

video::-webkit-media-controls-time-remaining-display {
  display: none;
}

video::-webkit-media-controls-mute-button {
  display: none;
}

video::-webkit-media-controls-toggle-closed-captions-button {
  display: none;
}

video::-webkit-media-controls-enclosure {
  display: none;
}

video::-webkit-media-controls-volume-slider {
  display: none;
}
</style>
